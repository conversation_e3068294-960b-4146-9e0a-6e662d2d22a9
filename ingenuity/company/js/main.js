/**
 * Consolidated JavaScript file for Company Website
 * Includes: Main functionality, Country Selector, Changelog Modal, Tokenizer, and Workspace
 * Minimal functionality to enhance user experience without adding noise
 * Enhanced with accessibility features
 */

document.addEventListener('DOMContentLoaded', function() {
  // Set active navigation link based on current page
  setActiveNavLink();

  // Initialize smooth scrolling for anchor links
  initSmoothScroll();

  // Initialize image lazy loading
  initLazyLoading();

  // Initialize page transitions
  initPageTransitions();

  // Initialize keyboard navigation
  initKeyboardNavigation();

  // Initialize focus visibility
  initFocusVisibility();
});

/**
 * Set the active navigation link based on the current page
 */
function setActiveNavLink() {
  const currentPath = window.location.pathname;
  const navLinks = document.querySelectorAll('nav a');

  navLinks.forEach(link => {
    const linkPath = link.getAttribute('href');
    if (currentPath.endsWith(linkPath)) {
      link.classList.add('active');
    }
  });

  // If no link is active and we're on the home page
  if (currentPath === '/' || currentPath.endsWith('index.html')) {
    const homeLink = document.querySelector('nav a[href="index.html"]');
    if (homeLink) {
      homeLink.classList.add('active');
    }
  }
}

// Agent animation is now handled in country-selector.js

/**
 * This function is no longer used - we've switched to pure CSS animations
 * for a cleaner, more harmonious visual experience with no noise or redundancy
 */
function typeText(element, text, speed, callback) {
  // Function kept for reference but not used
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScroll() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('href');
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
}

/**
 * Initialize lazy loading for images
 */
function initLazyLoading() {
  // Check if the browser supports IntersectionObserver
  if ('IntersectionObserver' in window) {
    const lazyImages = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      });
    });

    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    const lazyImages = document.querySelectorAll('img[data-src]');

    lazyImages.forEach(img => {
      img.src = img.dataset.src;
      img.removeAttribute('data-src');
    });
  }
}

/**
 * Initialize page transitions with blur-to-clarity effect
 */
function initPageTransitions() {
  // Add transition-fade class to main content
  const mainContent = document.querySelector('main');
  if (mainContent) {
    mainContent.classList.add('transition-fade');
  }

  // Handle all internal link clicks
  document.addEventListener('click', function(e) {
    // Find closest anchor tag
    const link = e.target.closest('a');

    // If it's an internal link (not external, not anchor, not javascript)
    if (link &&
        link.href.startsWith(window.location.origin) &&
        !link.href.includes('#') &&
        !link.href.startsWith('javascript:') &&
        !link.target) {

      e.preventDefault();

      // Start transition animation
      document.documentElement.classList.add('is-animating');

      // After animation completes, navigate to the new page
      setTimeout(function() {
        window.location.href = link.href;
      }, 400); // Match this to your CSS transition time
    }
  });

  // When page loads, remove the animation class
  window.addEventListener('pageshow', function() {
    document.documentElement.classList.remove('is-animating');
  });
}

/**
 * Initialize keyboard navigation for dropdown menus and interactive elements
 */
function initKeyboardNavigation() {
  // Handle dropdown menu keyboard navigation
  const moreButton = document.querySelector('.more-button');
  const dropdownMenu = document.querySelector('.nav-dropdown');

  if (moreButton && dropdownMenu) {
    // Convert div to button for better accessibility if not already
    if (moreButton.tagName !== 'BUTTON') {
      const newButton = document.createElement('button');
      newButton.className = moreButton.className;
      newButton.innerHTML = moreButton.innerHTML;
      newButton.setAttribute('aria-expanded', 'false');
      newButton.setAttribute('aria-controls', 'dropdown-menu');
      moreButton.parentNode.replaceChild(newButton, moreButton);
      moreButton = newButton;
    }

    // Toggle dropdown with keyboard
    moreButton.addEventListener('click', function() {
      const expanded = this.getAttribute('aria-expanded') === 'true' || false;
      this.setAttribute('aria-expanded', !expanded);

      if (!expanded) {
        // If opening the dropdown, focus the first item
        setTimeout(() => {
          const firstLink = dropdownMenu.querySelector('a');
          if (firstLink) firstLink.focus();
        }, 100);
      }
    });

    // Close dropdown when pressing Escape
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && moreButton.getAttribute('aria-expanded') === 'true') {
        moreButton.setAttribute('aria-expanded', 'false');
        moreButton.focus();
      }
    });

    // Handle arrow key navigation within dropdown
    dropdownMenu.addEventListener('keydown', function(e) {
      const links = Array.from(this.querySelectorAll('a'));
      const currentIndex = links.indexOf(document.activeElement);

      if (e.key === 'ArrowDown') {
        e.preventDefault();
        const nextIndex = (currentIndex + 1) % links.length;
        links[nextIndex].focus();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        const prevIndex = (currentIndex - 1 + links.length) % links.length;
        links[prevIndex].focus();
      } else if (e.key === 'Tab' && e.shiftKey && currentIndex === 0) {
        // If tabbing backwards from first item, close dropdown and focus button
        e.preventDefault();
        moreButton.setAttribute('aria-expanded', 'false');
        moreButton.focus();
      } else if (e.key === 'Tab' && !e.shiftKey && currentIndex === links.length - 1) {
        // If tabbing forward from last item, close dropdown
        moreButton.setAttribute('aria-expanded', 'false');
      }
    });
  }
}

/**
 * Initialize focus visibility to only show focus styles when using keyboard
 */
function initFocusVisibility() {
  // Add class to body to indicate JS is enabled
  document.body.classList.add('js-focus-visible');

  // Track whether user is using keyboard or mouse
  let usingKeyboard = false;

  // Set to true when user presses Tab
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
      usingKeyboard = true;
      document.body.classList.add('user-is-tabbing');
    }
  });

  // Set to false when user clicks with mouse
  document.addEventListener('mousedown', function() {
    usingKeyboard = false;
    document.body.classList.remove('user-is-tabbing');
  });

  // Add focus-visible class to elements when focused with keyboard
  document.addEventListener('focusin', function(e) {
    if (usingKeyboard) {
      e.target.classList.add('focus-visible');
    }
  });

  // Remove focus-visible class when focus moves away
  document.addEventListener('focusout', function(e) {
    e.target.classList.remove('focus-visible');
  });
}

/* ========================================
   COUNTRY SELECTOR FUNCTIONALITY
   ======================================== */

/**
 * Country Selection Modal with Validation
 * Shows only on first visit, respects localStorage
 * Fixed to ensure it only shows once across all pages
 * Enhanced with accessibility features
 */
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, checking if modal should be shown');

  // Debug localStorage
  try {
    console.log('Current localStorage state:');
    console.log('- selectedCountry:', localStorage.getItem('selectedCountry'));
    console.log('- cacheEnabled:', localStorage.getItem('cacheEnabled'));
  } catch (e) {
    console.error('Error accessing localStorage:', e);
  }

  // Get modal element
  const modal = document.getElementById('country-modal');
  const countrySelect = document.getElementById('country-select');
  const enableCacheCheckbox = document.getElementById('enable-cache');
  const confirmButton = document.getElementById('confirm-country');

  if (!modal || !countrySelect) {
    console.error('Required elements not found!');
    return;
  }

  // Check if user has already selected a country - use a more robust check
  // This will work across all pages including index.html
  const hasSelectedCountry = localStorage.getItem('selectedCountry');
  console.log('Has selected country?', hasSelectedCountry ? 'YES' : 'NO');
  console.log('Current URL:', window.location.href);

  // Only show modal if user hasn't selected a country yet
  if (!hasSelectedCountry) {
    console.log('No country selection found in localStorage, showing modal');

    // Show the modal with a slight delay for better user experience
    setTimeout(() => {
      // Show the modal
      modal.style.display = 'flex';
      modal.style.opacity = '1';
      modal.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';

      // Get the modal content
      const modalContent = modal.querySelector('.country-modal-content');
      if (modalContent) {
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'translateY(0) scale(1)';
      }

      // Focus the first focusable element in the modal
      countrySelect.focus();

      // Trap focus within modal
      trapFocusInModal(modal);
    }, 300);
  } else {
    console.log('Country already selected:', hasSelectedCountry, '- not showing modal');
    // Ensure modal is hidden
    if (modal) {
      modal.style.display = 'none';
      modal.setAttribute('aria-hidden', 'true');
    }
  }

  // Add event listener to the confirm button
  if (confirmButton) {
    confirmButton.addEventListener('click', function() {
      // Validate country selection
      if (!countrySelect.value) {
        // Show error - add red border to select
        countrySelect.style.borderColor = 'var(--color-error)';
        countrySelect.setAttribute('aria-invalid', 'true');

        // Add shake animation for better feedback
        countrySelect.style.animation = 'shake 0.5s cubic-bezier(.36,.07,.19,.97) both';
        setTimeout(() => {
          countrySelect.style.animation = '';
        }, 500);

        // Announce error to screen readers
        const errorMessage = document.createElement('div');
        errorMessage.className = 'sr-only';
        errorMessage.setAttribute('aria-live', 'assertive');
        errorMessage.textContent = 'Please select a country before continuing.';
        modal.appendChild(errorMessage);

        // Remove error message after it's been announced
        setTimeout(() => {
          modal.removeChild(errorMessage);
        }, 3000);

        // Focus the select element
        countrySelect.focus();

        return; // Stop execution if no country selected
      }

      // Get selected country and cache setting
      const selectedCountry = countrySelect.value;
      const cacheEnabled = enableCacheCheckbox.checked;

      // Store in localStorage
      localStorage.setItem('selectedCountry', selectedCountry);
      localStorage.setItem('cacheEnabled', cacheEnabled ? 'true' : 'false');

      // Hide the modal
      modal.style.opacity = '0';
      modal.setAttribute('aria-hidden', 'true');
      setTimeout(function() {
        modal.style.display = 'none';
        document.body.style.overflow = '';

        // Start the agent animation after the modal is hidden
        startAgentAnimation();
      }, 400);

      // Show confirmation with country name
      const countryName = getCountryName(selectedCountry);

      // Use a more accessible confirmation method
      const confirmationMessage = document.createElement('div');
      confirmationMessage.className = 'sr-only';
      confirmationMessage.setAttribute('aria-live', 'polite');
      confirmationMessage.textContent = `Country selection confirmed: ${countryName}! Cache settings ${cacheEnabled ? 'enabled' : 'disabled'}.`;
      document.body.appendChild(confirmationMessage);

      // Remove confirmation message after it's been announced
      setTimeout(() => {
        document.body.removeChild(confirmationMessage);
      }, 3000);
    });
  }

  // Add event listener to select for validation
  countrySelect.addEventListener('change', function() {
    // Remove error state when user selects a country
    if (this.value) {
      this.style.borderColor = '';
      this.setAttribute('aria-invalid', 'false');
    }
  });

  // Close modal on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.style.display === 'flex') {
      // Don't close if this is the first visit - force a selection
      if (!hasSelectedCountry) {
        // Focus the select element
        countrySelect.focus();
        return;
      }

      // Otherwise close the modal
      modal.style.opacity = '0';
      modal.setAttribute('aria-hidden', 'true');
      setTimeout(function() {
        modal.style.display = 'none';
        document.body.style.overflow = '';
      }, 400);
    }
  });

  // Add shake animation style
  addShakeAnimation();
});

/**
 * Trap focus within the modal dialog for keyboard accessibility
 * @param {HTMLElement} modal - The modal element
 */
function trapFocusInModal(modal) {
  // Get all focusable elements
  const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  // Handle tab key to trap focus
  modal.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
      // Shift + Tab on first element should loop to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
      // Tab on last element should loop to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  });
}

/**
 * Get country name from country code
 * @param {string} code - The country code
 * @returns {string} The country name
 */
function getCountryName(code) {
  const countries = {
    'us': 'United States',
    'ca': 'Canada',
    'uk': 'United Kingdom',
    'au': 'Australia',
    'de': 'Germany',
    'fr': 'France',
    'jp': 'Japan',
    'in': 'India',
    'br': 'Brazil',
    'other': 'Somewhere Else Entirely'
  };

  return countries[code] || 'Unknown';
}

/**
 * Add shake animation style to the document
 */
function addShakeAnimation() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes shake {
      10%, 90% { transform: translateX(-1px); }
      20%, 80% { transform: translateX(2px); }
      30%, 50%, 70% { transform: translateX(-3px); }
      40%, 60% { transform: translateX(3px); }
    }
  `;
  document.head.appendChild(style);
}

/**
 * Start the agent animation by adding animation classes
 */
function startAgentAnimation() {
  // Only run on the homepage
  if (window.location.pathname === '/' ||
      window.location.pathname.endsWith('index.html')) {

    // Add animation classes to each element
    const agentPrompt = document.querySelector('.agent-prompt');
    const agentResponse = document.querySelector('.agent-response');
    const agentResponse2 = document.querySelector('.agent-response-2');
    const agentResponse3 = document.querySelector('.agent-response-3');

    if (agentPrompt) agentPrompt.classList.add('animate-prompt');
    if (agentResponse) agentResponse.classList.add('animate-response');
    if (agentResponse2) agentResponse2.classList.add('animate-response-2');
    if (agentResponse3) agentResponse3.classList.add('animate-response-3');
  }
}

// If user has already selected a country, start the animation immediately
document.addEventListener('DOMContentLoaded', function() {
  const hasSelectedCountry = localStorage.getItem('selectedCountry');
  if (hasSelectedCountry) {
    // Small delay to ensure the page is fully loaded
    setTimeout(startAgentAnimation, 500);
  }
});

// Add a hidden debug function to reset country selection (for testing)
// Can be called from console: resetCountrySelection()
window.resetCountrySelection = function() {
  localStorage.removeItem('selectedCountry');
  localStorage.removeItem('cacheEnabled');
  alert('Country selection reset. Refresh the page to see the modal again.');
};

/* ========================================
   CHANGELOG MODAL FUNCTIONALITY
   ======================================== */

/**
 * Changelog Modal Functionality
 */
document.addEventListener('DOMContentLoaded', function() {
  // Create the changelog button
  const changelogButton = document.createElement('div');
  changelogButton.className = 'changelog-button';
  changelogButton.setAttribute('aria-label', 'View changelog');
  changelogButton.setAttribute('role', 'button');
  changelogButton.setAttribute('tabindex', '0');
  changelogButton.innerHTML = '<img src="/svg/delta.svg" alt="Delta icon">';
  document.body.appendChild(changelogButton);

  // Create the changelog modal
  const changelogModal = document.createElement('div');
  changelogModal.className = 'changelog-modal';
  changelogModal.setAttribute('role', 'dialog');
  changelogModal.setAttribute('aria-modal', 'true');
  changelogModal.setAttribute('aria-labelledby', 'changelog-title');

  // Create the modal content
  const changelogContent = document.createElement('div');
  changelogContent.className = 'changelog-content';

  // Create the close button
  const closeButton = document.createElement('div');
  closeButton.className = 'changelog-close';
  closeButton.setAttribute('aria-label', 'Close changelog');
  closeButton.setAttribute('role', 'button');
  closeButton.setAttribute('tabindex', '0');

  // Create the title
  const title = document.createElement('h2');
  title.id = 'changelog-title';
  title.className = 'changelog-title';
  title.textContent = 'Changelog';

  // Create the body
  const body = document.createElement('div');
  body.className = 'changelog-body';
  body.innerHTML = '<p>Loading changelog...</p>';

  // Append elements
  changelogContent.appendChild(closeButton);
  changelogContent.appendChild(title);
  changelogContent.appendChild(body);
  changelogModal.appendChild(changelogContent);
  document.body.appendChild(changelogModal);

  // Function to fetch and parse the changelog
  function fetchChangelog() {
    fetch('/CHANGELOG.md')
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to load changelog');
        }
        return response.text();
      })
      .then(markdown => {
        // Simple markdown to HTML conversion
        let html = markdown
          // Convert headers
          .replace(/^# (.*$)/gm, '<h1>$1</h1>')
          .replace(/^## (.*$)/gm, '<h2>$1</h2>')
          .replace(/^### (.*$)/gm, '<h3>$1</h3>')
          // Convert links
          .replace(/\[(.*?)\]\((.*?)\)/gm, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
          // Convert lists
          .replace(/^- (.*$)/gm, '<li>$1</li>')
          .replace(/(<li>.*<\/li>\n)+/gm, '<ul>$&</ul>')
          // Convert paragraphs
          .replace(/^(?!<[uh][1-3]>|<li>|<ul>)(.+)$/gm, '<p>$1</p>');

        body.innerHTML = html;
      })
      .catch(error => {
        body.innerHTML = `<p>Error loading changelog: ${error.message}</p>`;
      });
  }

  // Event listeners
  changelogButton.addEventListener('click', function() {
    changelogModal.classList.add('show');
    fetchChangelog();
    // Trap focus in modal
    changelogContent.focus();
  });

  changelogButton.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      changelogModal.classList.add('show');
      fetchChangelog();
      closeButton.focus();
    }
  });

  closeButton.addEventListener('click', function() {
    changelogModal.classList.remove('show');
    changelogButton.focus();
  });

  closeButton.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      changelogModal.classList.remove('show');
      changelogButton.focus();
    }
  });

  // Close modal when clicking outside content
  changelogModal.addEventListener('click', function(e) {
    if (e.target === changelogModal) {
      changelogModal.classList.remove('show');
      changelogButton.focus();
    }
  });

  // Close modal with Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && changelogModal.classList.contains('show')) {
      changelogModal.classList.remove('show');
      changelogButton.focus();
    }
  });
});

/* ========================================
   WORKSPACE FUNCTIONALITY
   ======================================== */

/**
 * Workspace JavaScript functionality
 * Handles login form validation and submission
 */
document.addEventListener('DOMContentLoaded', function() {
  // Get form elements
  const loginForm = document.querySelector('.login-form');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');

  if (loginForm) {
    // Add form submission handler
    loginForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Validate form
      let isValid = true;

      // Validate email
      if (!validateEmail(emailInput.value)) {
        showError(emailInput, 'Please enter a valid email address');
        isValid = false;
      } else {
        clearError(emailInput);
      }

      // Validate password
      if (passwordInput.value.length < 8) {
        showError(passwordInput, 'Password must be at least 8 characters');
        isValid = false;
      } else {
        clearError(passwordInput);
      }

      // If form is valid, simulate login
      if (isValid) {
        simulateLogin();
      }
    });

    // Add input event listeners for real-time validation
    emailInput.addEventListener('input', function() {
      if (validateEmail(this.value)) {
        clearError(this);
      }
    });

    passwordInput.addEventListener('input', function() {
      if (this.value.length >= 8) {
        clearError(this);
      }
    });
  }
});

/**
 * Validate email format
 * @param {string} email - The email to validate
 * @returns {boolean} - Whether the email is valid
 */
function validateEmail(email) {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
}

/**
 * Show error message for an input
 * @param {HTMLElement} input - The input element
 * @param {string} message - The error message
 */
function showError(input, message) {
  // Remove any existing error message
  clearError(input);

  // Add aria-invalid attribute
  input.setAttribute('aria-invalid', 'true');

  // Create error message element
  const errorElement = document.createElement('div');
  errorElement.className = 'error-message';
  errorElement.textContent = message;
  errorElement.setAttribute('role', 'alert');

  // Insert error message after input
  input.parentNode.insertBefore(errorElement, input.nextSibling);
}

/**
 * Clear error message for an input
 * @param {HTMLElement} input - The input element
 */
function clearError(input) {
  // Remove aria-invalid attribute
  input.removeAttribute('aria-invalid');

  // Remove any existing error message
  const errorElement = input.parentNode.querySelector('.error-message');
  if (errorElement) {
    errorElement.parentNode.removeChild(errorElement);
  }
}

/**
 * Simulate login process
 * This is just a demo - in a real application, this would send a request to the server
 */
function simulateLogin() {
  // Get form and submit button
  const loginForm = document.querySelector('.login-form');
  const submitButton = loginForm.querySelector('button[type="submit"]');

  // Disable form and show loading indicator
  submitButton.disabled = true;
  const originalButtonText = submitButton.textContent;
  submitButton.innerHTML = '<span class="loading"></span> Signing in...';

  // Simulate API request delay
  setTimeout(function() {
    // Create success message
    const successElement = document.createElement('div');
    successElement.className = 'success-message';
    successElement.textContent = 'Login successful! Redirecting to dashboard...';
    successElement.setAttribute('role', 'alert');

    // Add success message to form
    loginForm.appendChild(successElement);

    // Simulate redirect delay
    setTimeout(function() {
      // In a real application, this would redirect to the dashboard
      alert('This is a demo. In a real application, you would be redirected to your dashboard.');

      // Reset form
      submitButton.disabled = false;
      submitButton.textContent = originalButtonText;
      loginForm.reset();

      // Remove success message
      successElement.parentNode.removeChild(successElement);
    }, 2000);
  }, 1500);
}

/* ========================================
   TOKENIZER FUNCTIONALITY
   ======================================== */

/**
 * GPT-2 Tokenizer
 * A tokenizer that analyzes text using the GPT-2 model
 */
class GPT2Tokenizer {
  constructor() {
    // API endpoint for the GPT-2 model
    this.apiEndpoint = '/api/analyze';

    // Fallback to client-side analysis if API is unavailable
    this.useClientSideFallback = true;

    // The golden ratio for fallback calculations
    this.PHI = 1.618;

    // Common English word frequencies for analysis
    this.commonWords = new Set([
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'I',
      'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at'
    ]);

    // Redundant phrases that reduce efficiency
    this.redundantPhrases = [
      'very', 'really', 'actually', 'basically', 'literally', 'just',
      'simply', 'quite', 'rather', 'somewhat', 'kind of', 'sort of'
    ];
  }

  /**
   * Analyze text using the Llama model
   * @param {string} text - The text to analyze
   * @returns {Promise<object>} Analysis results
   */
  async analyze(text) {
    try {
      // Try to use the Llama API
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.warn('API call failed, using client-side fallback:', error);

      if (this.useClientSideFallback) {
        // Use client-side analysis as fallback
        return this.analyzeClientSide(text);
      } else {
        throw error;
      }
    }
  }

  /**
   * Analyze text using client-side algorithms (fallback)
   * @param {string} text - The text to analyze
   * @returns {object} Analysis results
   */
  analyzeClientSide(text) {
    // Basic text metrics
    const charCount = text.length;
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const wordCount = words.length;
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const sentenceCount = sentences.length;

    // Calculate token count (more sophisticated than simple word count)
    const tokenCount = this.countTokens(text);

    // Calculate golden ratio alignment
    const alignment = this.calculateGoldenRatioAlignment(text);

    // Calculate efficiency
    const efficiency = this.calculateEfficiency(text);

    // Calculate optimization potential
    const potential = this.calculateOptimizationPotential(text);

    return {
      tokenCount,
      efficiency,
      potential,
      alignment,
      charCount,
      wordCount,
      sentenceCount,
      model: 'GPT-2 (Fallback)'
    };
  }

  /**
   * Count tokens in text using a more sophisticated algorithm
   * @param {string} text - The text to count tokens in
   * @returns {number} Estimated token count
   */
  countTokens(text) {
    // This is a simplified version of how tokenization works
    // Real tokenizers use more complex algorithms

    // Split text into words
    const words = text.split(/\s+/).filter(w => w.length > 0);

    // Count special tokens (punctuation, etc.)
    const specialTokens = (text.match(/[.,!?;:()[\]{}""''`\-–—]/g) || []).length;

    // Count numbers as separate tokens
    const numbers = (text.match(/\b\d+(?:\.\d+)?\b/g) || []).length;

    // Count capitalized words (names, etc.) which might be treated differently
    const capitalizedWords = words.filter(w => /^[A-Z][a-z]*$/.test(w)).length;

    // Base token count (roughly 4 characters per token for English)
    let baseTokenCount = Math.ceil(text.length / 4);

    // Adjust based on special cases
    baseTokenCount += Math.floor(specialTokens * 0.5);
    baseTokenCount += Math.floor(numbers * 0.3);
    baseTokenCount += Math.floor(capitalizedWords * 0.2);

    return Math.max(1, baseTokenCount);
  }

  /**
   * Calculate how well the text aligns with golden ratio principles
   * @param {string} text - The text to analyze
   * @returns {number} Alignment score (0-100)
   */
  calculateGoldenRatioAlignment(text) {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    // Calculate sentence lengths
    const sentenceLengths = sentences.map(s => s.trim().split(/\s+/).filter(w => w.length > 0).length);
    const avgSentenceLength = sentenceLengths.reduce((sum, len) => sum + len, 0) / sentenceLengths.length || 0;

    // Calculate word lengths
    const wordLengths = words.map(w => w.length);
    const avgWordLength = wordLengths.reduce((sum, len) => sum + len, 0) / wordLengths.length || 0;

    // Calculate paragraph structure (if applicable)
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const paragraphLengths = paragraphs.map(p => p.trim().split(/\s+/).filter(w => w.length > 0).length);
    const avgParagraphLength = paragraphLengths.reduce((sum, len) => sum + len, 0) / paragraphLengths.length || 0;

    // Calculate how close these metrics are to golden ratio ideals
    // Ideal sentence length: ~16 words (phi * 10)
    const sentenceRatioDiff = Math.abs(avgSentenceLength - (this.PHI * 10)) / (this.PHI * 10);

    // Ideal word length: ~5 characters (phi * 3)
    const wordRatioDiff = Math.abs(avgWordLength - (this.PHI * 3)) / (this.PHI * 3);

    // Ideal paragraph length: ~81 words (phi^4)
    const paragraphRatioDiff = Math.abs(avgParagraphLength - Math.pow(this.PHI, 4)) / Math.pow(this.PHI, 4);

    // Calculate overall alignment score (higher is better)
    const alignmentScore = 100 - ((sentenceRatioDiff + wordRatioDiff + paragraphRatioDiff) * 33);

    return Math.min(100, Math.max(65, alignmentScore));
  }

  /**
   * Calculate the efficiency of the text
   * @param {string} text - The text to analyze
   * @returns {number} Efficiency score (0-100)
   */
  calculateEfficiency(text) {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const wordCount = words.length;

    if (wordCount === 0) {
      return 70; // Default for empty text
    }

    // Calculate average word length
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / wordCount;

    // Calculate common word usage (higher is more efficient)
    const commonWordCount = words.filter(w => this.commonWords.has(w.toLowerCase())).length;
    const commonWordRatio = commonWordCount / wordCount;

    // Calculate sentence complexity
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;

    // Ideal word length is close to golden ratio * 2
    const idealWordLength = this.PHI * 2;
    const wordLengthEfficiency = 100 - (Math.abs(avgWordLength - idealWordLength) / idealWordLength * 30);

    // Ideal common word ratio is around 0.5 (balance between common and specific words)
    const commonWordEfficiency = 100 - (Math.abs(commonWordRatio - 0.5) * 100);

    // Ideal sentence length is around golden ratio * 10
    const idealSentenceLength = this.PHI * 10;
    const sentenceLengthEfficiency = avgSentenceLength > 0
      ? 100 - (Math.abs(avgSentenceLength - idealSentenceLength) / idealSentenceLength * 30)
      : 70; // Default for no sentences

    // Calculate overall efficiency
    const efficiency = (wordLengthEfficiency * 0.4) + (commonWordEfficiency * 0.3) + (sentenceLengthEfficiency * 0.3);

    return Math.min(95, Math.max(70, efficiency));
  }

  /**
   * Calculate the optimization potential of the text
   * @param {string} text - The text to analyze
   * @returns {number} Optimization potential (0-100)
   */
  calculateOptimizationPotential(text) {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const wordCount = words.length;

    if (wordCount === 0) {
      return 80; // Default for empty text
    }

    // Count redundant phrases
    let redundantCount = 0;
    this.redundantPhrases.forEach(phrase => {
      const regex = new RegExp(`\\b${phrase}\\b`, 'gi');
      const matches = text.match(regex) || [];
      redundantCount += matches.length;
    });

    // Calculate redundancy factor
    const redundancyFactor = Math.min(1, redundantCount / wordCount * 10);

    // Calculate passive voice usage
    const passiveVoiceCount = (text.match(/\b(?:am|is|are|was|were|be|been|being)\s+\w+ed\b/g) || []).length;
    const passiveVoiceFactor = Math.min(1, passiveVoiceCount / wordCount * 20);

    // Calculate sentence variety
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const sentenceLengths = sentences.map(s => s.trim().split(/\s+/).filter(w => w.length > 0).length);
    const sentenceLengthVariety = this.calculateStandardDeviation(sentenceLengths);
    const sentenceVarietyFactor = Math.min(1, Math.max(0, 1 - sentenceLengthVariety / 10));

    // Calculate overall optimization potential
    const basePotential = 75;
    const potential = basePotential + (redundancyFactor * 10) + (passiveVoiceFactor * 10) + (sentenceVarietyFactor * 5);

    return Math.min(98, Math.max(60, potential));
  }

  /**
   * Calculate standard deviation of an array of numbers
   * @param {Array<number>} values - Array of numbers
   * @returns {number} Standard deviation
   */
  calculateStandardDeviation(values) {
    if (values.length === 0) return 0;

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squareDiffs = values.map(value => Math.pow(value - avg, 2));
    const avgSquareDiff = squareDiffs.reduce((sum, val) => sum + val, 0) / squareDiffs.length;
    return Math.sqrt(avgSquareDiff);
  }
}

// Export the tokenizer
window.GoldenRatioTokenizer = GPT2Tokenizer;
