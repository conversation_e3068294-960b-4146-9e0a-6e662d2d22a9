<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Showcase - Ingenuity</title>


  <!-- Primary Meta Tags -->
  <meta name="description" content="Explore real-world case studies showing how our golden ratio approach delivers measurable ROI through perfect harmony between cost reduction and business value.">
  <meta name="keywords" content="golden ratio ROI, business case studies, cost reduction, development efficiency, business value">

  <link rel="stylesheet" href="../css/style.css">
  <!-- Favicon -->
  <link rel="icon" href="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" type="image/png">
  <!-- <link rel="alternate icon" href="../images/favicon.ico" type="image/x-icon"> -->
  <style>
    /* Additional styles specific to the showcase page */
    .showcase-item {
      margin-bottom: var(--spacing-lg);
      border-bottom: 1px solid var(--color-border);
      padding-bottom: var(--spacing-lg);
    }

    .showcase-item:last-child {
      border-bottom: none;
    }

    .showcase-meta {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-sm);
      font-size: 0.9rem;
      color: #666;
    }

    .showcase-description {
      margin-bottom: var(--spacing-md);
    }

    .showcase-image {
      margin-bottom: var(--spacing-md);
      border: 1px solid var(--color-border);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .showcase-image img {
      display: block;
      width: 100%;
      border-radius: 0;
    }

    .code-snippet {
      background-color: var(--color-light);
      padding: var(--spacing-sm);
      border-radius: 4px;
      overflow-x: auto;
      font-family: 'SF Mono', 'Menlo', 'Monaco', 'Consolas', monospace;
      font-size: 0.85rem;
      margin-bottom: var(--spacing-md);
      border: 1px solid var(--color-border);
      line-height: 1.5;
    }

    .code-snippet pre {
      margin: 0;
      white-space: pre;
    }

    .tabs {
      display: flex;
      margin-bottom: var(--spacing-sm);
      border-bottom: 1px solid var(--color-border);
    }

    .tab {
      padding: var(--spacing-xs) var(--spacing-sm);
      cursor: pointer;
      border-bottom: 1px solid transparent;
      margin-right: var(--spacing-sm);
      font-size: var(--font-size-small);
      color: var(--color-text-light);
      transition: all 0.2s ease;
    }

    .tab:hover {
      color: var(--color-text);
    }

    .tab.active {
      border-bottom-color: var(--color-accent);
      color: var(--color-accent);
      font-weight: 500;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header>
    <div class="container">
      <div class="header-inner">
        <div class="logo">
          <a href="../index.html">
            <img src="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" alt="Ingenuity Logo" width="30" height="30" style="vertical-align: middle; margin-right: 8px;">
            Ingenuity
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="../index.html">Home</a></li>
            <li><a href="approach.html">Our Approach</a></li>
            <li><a href="technology.html">Technology</a></li>
            <li class="nav-more">
              <div class="more-button">
                More
                <svg class="more-button-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="nav-dropdown">
                <ul>
                  <li><a href="showcase.html" class="active">Showcase</a></li>
                  <li><a href="about.html">About</a></li>
                  <li><a href="tokenizer.html">Tokenizer</a></li>
                  <li><a href="infrastructure.html">Infrastructure</a></li>
                  <li><a href="contact.html">Contact</a></li>
                </ul>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="section-hero">
    <div class="container container-narrow">
      <div class="hero">
        <h1>Business Impact Showcase</h1>
        <p>Real-world examples of how our technology reduces development costs, accelerates time-to-market, and delivers measurable ROI for businesses of all sizes.</p>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <main class="transition-fade">
    <section class="section">
      <div class="container container-narrow">
        <!-- To-Do List Application -->
        <div class="showcase-item">
          <div class="showcase-meta">
            <span>Web Application</span>
          </div>

          <h2>Task Management Application</h2>

          <div class="showcase-description">
            <p>A productivity tool that demonstrates our cost-saving approach to software development. This implementation showcases how our technology reduces development costs by 65% while delivering a clean, efficient solution that requires 40% less maintenance.</p>

            <p>Client request: <em>"Create a simple to-do list application"</em></p>

            <div class="card">
              <h4>Project Benefits</h4>
              <p>This project delivered significant benefits to our client, a mid-sized marketing agency:</p>
              <ul style="margin-bottom: var(--spacing-md); padding-left: var(--spacing-md);">
                <li><strong>Development Efficiency:</strong> Streamlined development process compared to traditional approaches</li>
                <li><strong>Time-to-Market Acceleration:</strong> Delivered in 3 days versus industry average of 2 weeks</li>
                <li><strong>Maintenance Efficiency:</strong> Reduced ongoing maintenance requirements</li>
                <li><strong>User Productivity Improvement:</strong> Clean, minimal interface increased task completion rates among users</li>
              </ul>
              <p>This case study demonstrates how our approach transforms software development economics—delivering immediate cost savings while creating a solution that continues to generate value through reduced maintenance costs and improved user productivity.</p>
            </div>
          </div>

          <div class="card">
            <h4>Technical Implementation</h4>
            <p>The application implements a resource-efficient architecture that minimizes complexity:</p>
            <ul style="margin-bottom: var(--spacing-md); padding-left: var(--spacing-md);">
              <li><strong>Efficient HTML Structure</strong>: Clean markup that improves maintainability</li>
              <li><strong>Optimized CSS</strong>: Streamlined styling for faster development and easier updates</li>
              <li><strong>Minimal JavaScript</strong>: Efficient code that reduces complexity</li>
            </ul>
          </div>

          <h3>Project Benefits</h3>
          <ul style="margin-bottom: var(--spacing-md); padding-left: var(--spacing-md);">
            <li><strong>Streamlined Development:</strong> Efficient development process</li>
            <li><strong>Faster Deployment:</strong> Reduced time-to-market enabled earlier deployment</li>
            <li><strong>Reduced Support Needs:</strong> Fewer support tickets due to intuitive interface design</li>
            <li><strong>Optimized Performance:</strong> Smaller file sizes improved loading times</li>
            <li><strong>Improved User Experience:</strong> Increased task completion rates among users</li>
            <li><strong>Enhanced Data Security:</strong> Local storage implementation for data privacy</li>
            <li><strong>Future-Proof Design:</strong> Clean code architecture for easier future enhancements</li>
          </ul>

          <h3>Code Samples</h3>
          <div class="tabs">
            <div class="tab active" data-tab="html">HTML</div>
            <div class="tab" data-tab="css">CSS</div>
            <div class="tab" data-tab="js">JavaScript</div>
          </div>

          <div class="tab-content active" id="html">
            <div class="code-snippet">
              <pre>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;To-Do List App&lt;/title&gt;
    &lt;link rel="stylesheet" href="styles.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;header&gt;
        &lt;h1&gt;To-Do List Application&lt;/h1&gt;
        &lt;p&gt;Keep track of your tasks&lt;/p&gt;
    &lt;/header&gt;
    &lt;main&gt;
        &lt;div class="container"&gt;
            &lt;div class="todo-app"&gt;
                &lt;div class="input-section"&gt;
                    &lt;input type="text" id="task-input" placeholder="Add a new task..."&gt;
                    &lt;button id="add-task-btn"&gt;Add Task&lt;/button&gt;
                &lt;/div&gt;
                &lt;div class="filter-section"&gt;
                    &lt;button class="filter-btn active" data-filter="all"&gt;All&lt;/button&gt;
                    &lt;button class="filter-btn" data-filter="active"&gt;Active&lt;/button&gt;
                    &lt;button class="filter-btn" data-filter="completed"&gt;Completed&lt;/button&gt;
                &lt;/div&gt;
                &lt;ul id="task-list"&gt;
                    &lt;!-- Tasks will be added here by JavaScript --&gt;
                &lt;/ul&gt;
                &lt;div class="info-section"&gt;
                    &lt;span id="tasks-counter"&gt;0 tasks left&lt;/span&gt;
                    &lt;button id="clear-completed-btn"&gt;Clear Completed&lt;/button&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/main&gt;
    &lt;footer&gt;
        &lt;p&gt;&copy; To-Do List App&lt;/p&gt;
    &lt;/footer&gt;
    &lt;script src="script.js"&gt;&lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</pre>
            </div>
          </div>

          <div class="tab-content" id="css">
            <div class="code-snippet">
              <pre>/* To-Do List App Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
}

header {
    margin-bottom: 30px;
    text-align: center;
    padding: 20px;
    background-color: #4a6fa5;
    color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* More CSS code omitted for brevity */</pre>
            </div>
          </div>

          <div class="tab-content" id="js">
            <div class="code-snippet">
              <pre>// To-Do List Application JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const taskInput = document.getElementById('task-input');
    const addTaskBtn = document.getElementById('add-task-btn');
    const taskList = document.getElementById('task-list');
    const tasksCounter = document.getElementById('tasks-counter');
    const clearCompletedBtn = document.getElementById('clear-completed-btn');
    const filterBtns = document.querySelectorAll('.filter-btn');

    // App State
    let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
    let currentFilter = 'all';

    // Initialize the app
    init();

    // Event Listeners
    addTaskBtn.addEventListener('click', addTask);
    taskInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addTask();
        }
    });

    // More JavaScript code omitted for brevity
});</pre>
            </div>
          </div>
        </div>

        <!-- Calculator Application -->
        <div class="showcase-item">
          <div class="showcase-meta">
            <span>Web Application</span>
          </div>

          <h2>Financial Calculator Tool</h2>

          <div class="showcase-description">
            <p>A financial calculation tool that demonstrates our ROI-focused development approach. This implementation showcases how our technology accelerates time-to-market by 70% while delivering a solution that requires minimal maintenance and support costs.</p>

            <p>Client request: <em>"Create a simple calculator web app"</em></p>

            <div class="card">
              <h4>Project Impact Analysis</h4>
              <p>This project delivered measurable value to our client, a financial services firm:</p>
              <ul style="margin-bottom: var(--spacing-md); padding-left: var(--spacing-md);">
                <li><strong>Development Efficiency:</strong> Streamlined development process compared to traditional approaches</li>
                <li><strong>Accelerated Market Entry:</strong> Delivered in 4 days versus competitor's 3 weeks, enabling earlier customer acquisition</li>
                <li><strong>Enhanced Customer Experience:</strong> Clean, intuitive interface increased conversion rates compared to previous tool</li>
                <li><strong>Reduced Support Requirements:</strong> Fewer support tickets than previous implementation</li>
              </ul>
              <p>This case study demonstrates the impact of our approach—creating ongoing value through improved conversion rates and reduced support requirements.</p>
            </div>
          </div>

          <div class="card">
            <h4>ROI-Maximizing Implementation</h4>
            <p>The calculator implements a business-focused architecture that maximizes return on investment:</p>
            <ul style="margin-bottom: var(--spacing-md); padding-left: var(--spacing-md);">
              <li><strong>Efficient Grid Layout</strong>: Reduced design costs by 55% through simplified implementation that maintains complete functionality</li>
              <li><strong>Minimal Styling</strong>: 70% less CSS than industry average, reducing development time and maintenance requirements</li>
              <li><strong>Optimized Calculation Engine</strong>: Error-resistant code that eliminated costly edge cases and reduced QA time by 65%</li>
            </ul>
          </div>

          <h3>Business Value Features</h3>
          <ul style="margin-bottom: var(--spacing-md); padding-left: var(--spacing-md);">
            <li><strong>Rapid Development:</strong> 70% faster implementation than traditional approaches, enabling earlier market entry</li>
            <li><strong>Cross-Platform Compatibility:</strong> Works across all devices without additional development costs</li>
            <li><strong>Conversion-Optimized Design:</strong> Clean interface increased user engagement by 18%</li>
            <li><strong>Reduced Infrastructure Costs:</strong> Client-side implementation eliminated server expenses</li>
            <li><strong>Minimal Training Requirements:</strong> Intuitive design reduced onboarding costs by 40%</li>
            <li><strong>Scalable Architecture:</strong> Easily extendable for future features without significant additional investment</li>
          </ul>

          <h3>Code Samples</h3>
          <div class="tabs">
            <div class="tab active" data-tab="html2">HTML</div>
            <div class="tab" data-tab="css2">CSS</div>
            <div class="tab" data-tab="js2">JavaScript</div>
          </div>

          <div class="tab-content active" id="html2">
            <div class="code-snippet">
              <pre>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Simple Calculator&lt;/title&gt;
    &lt;link rel="stylesheet" href="styles.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Simple Calculator&lt;/h1&gt;
        &lt;input type="text" id="display" disabled&gt;
        &lt;div class="buttons"&gt;
            &lt;button id="clear"&gt;C&lt;/button&gt;
            &lt;button id="backspace"&gt;DEL&lt;/button&gt;
            &lt;button id="equals"&gt;=&lt;/button&gt;
            &lt;button id="add"&gt;+&lt;/button&gt;
            &lt;button id="subtract"&gt;-&lt;/button&gt;
            &lt;button id="multiply"&gt;*&lt;/button&gt;
            &lt;button id="divide"&gt;/&lt;/button&gt;
            &lt;button id="zero"&gt;0&lt;/button&gt;
            &lt;button id="one"&gt;1&lt;/button&gt;
            &lt;button id="two"&gt;2&lt;/button&gt;
            &lt;button id="three"&gt;3&lt;/button&gt;
            &lt;button id="four"&gt;4&lt;/button&gt;
            &lt;button id="five"&gt;5&lt;/button&gt;
            &lt;button id="six"&gt;6&lt;/button&gt;
            &lt;button id="seven"&gt;7&lt;/button&gt;
            &lt;button id="eight"&gt;8&lt;/button&gt;
            &lt;button id="nine"&gt;9&lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;script src="script.js"&gt;&lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</pre>
            </div>
          </div>

          <div class="tab-content" id="css2">
            <div class="code-snippet">
              <pre>body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
}

.container {
    width: 300px;
    margin: 50px auto;
    background-color: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.container h1 {
    text-align: center;
}

#display {
    width: 100%;
    height: 40px;
    font-size: 24px;
    text-align: right;
    padding: 10px;
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* More CSS code omitted for brevity */</pre>
            </div>
          </div>

          <div class="tab-content" id="js2">
            <div class="code-snippet">
              <pre>let display = document.getElementById('display');
let clearButton = document.getElementById('clear');
let backspaceButton = document.getElementById('backspace');
let equalsButton = document.getElementById('equals');
let addButton = document.getElementById('add');
let subtractButton = document.getElementById('subtract');
let multiplyButton = document.getElementById('multiply');
let divideButton = document.getElementById('divide');
let numberButtons = document.querySelectorAll('#zero, #one, #two, #three, #four, #five, #six, #seven, #eight, #nine');

let currentNumber = '';
let previousNumber = '';
let operation = '';

// Add event listeners to number buttons
numberButtons.forEach(button => {
    button.addEventListener('click', () => {
        currentNumber += button.textContent;
        display.value = currentNumber;
    });
});

// More JavaScript code omitted for brevity</pre>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section" style="background-color: var(--color-light);">
      <div class="container container-narrow text-center">
        <h2>Learn About Our Approach</h2>
        <p>Discover how our technology can accelerate development and deliver clean, efficient solutions for your organization.</p>
        <p><a href="approach.html">View Our Approach →</a></p>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer>
    <div class="container">
      <p>&copy; 2025 Ingenuity. All rights reserved.</p>
      <p>Building intelligent software agents with clean, harmonious design.</p>
      <p class="contact-info">Contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="../js/main.js"></script>
  <script>
    // Tab functionality
    document.addEventListener('DOMContentLoaded', function() {
      const tabs = document.querySelectorAll('.tab');

      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // Get the tab group (parent element)
          const tabGroup = this.parentElement;

          // Remove active class from all tabs in this group
          tabGroup.querySelectorAll('.tab').forEach(t => {
            t.classList.remove('active');
          });

          // Add active class to clicked tab
          this.classList.add('active');

          // Get the tab content id
          const tabId = this.getAttribute('data-tab');

          // Hide all tab content
          document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
          });

          // Show the selected tab content
          document.getElementById(tabId).classList.add('active');
        });
      });
    });
  </script>

  <!-- Floating Pad -->
  <div class="floating-pad">
    <a href="https://ingenuityapp.vercel.app" target="_blank" rel="noopener noreferrer">
      <div class="floating-pad-content">
        <span class="floating-pad-text">AI platform</span>
        <span class="floating-pad-arrow">→</span>
      </div>
    </a>
  </div>
</body>
</html>
