/*
 * Consolidated Stylesheet for Company Website
 * Includes: Main styles, Country Modal, Changelog Modal, SOTA page, and Workspace styles
 * Focus: Clean typography, minimal design, no visual noise
 */

/* Base Reset */
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Page Transition Effects */
html.is-animating .transition-fade {
  opacity: 0;
  filter: blur(10px);
  transform: translateY(15px);
}

.transition-fade {
  transition: opacity var(--transition-medium) ease,
              filter var(--transition-medium) ease,
              transform var(--transition-medium) ease;
  opacity: 1;
  filter: blur(0px);
  transform: translateY(0);
}

/* Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

:root {
  /* Color Variables - Ultimate Minimalist Palette with Improved Contrast */
  --color-background: #e8e8e8;
  --color-text: #111111;
  --color-text-light: #555555; /* Improved from #666666 for better contrast */
  --color-accent: #000000;
  --color-light: #fcfcfc;
  --color-border: #e0e0e0; /* Improved from #f5f5f5 for better visibility */
  --color-focus: #0066cc; /* Added for focus states */
  --color-focus-ring: rgba(0, 102, 204, 0.25); /* Added for focus rings */
  --color-error: #d32f2f; /* Added for error states */

  /* Typography Variables - Perfected for Ultimate Readability */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-base: 1rem;
  --font-size-small: 0.875rem;
  --font-size-large: 1.125rem;
  --line-height-base: 1.8;
  --line-height-heading: 1.2;
  --letter-spacing-tight: -0.03em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.01em;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;

  /* Spacing Variables - Golden Ratio Harmony */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.809rem;
  --spacing-md: 1.618rem;
  --spacing-lg: 2.618rem;
  --spacing-xl: 4.236rem;
  --spacing-xxl: 6.854rem;

  /* Container Width - Optimized for Perfect Reading */
  --container-width: 960px;
  --container-narrow: 620px;

  /* Animation Variables */
  --transition-fast: 0.2s;
  --transition-medium: 0.4s;
  --transition-slow: 0.6s;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-base);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0, "onum" 1, "lnum" 0, "dlig" 0;
  font-kerning: normal;
  -webkit-tap-highlight-color: transparent;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Layout - Refined for Better Spacing and Rhythm */
.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container-narrow {
  max-width: var(--container-narrow);
}

.section {
  padding: var(--spacing-lg) 0;
  margin-bottom: var(--spacing-md);
}

.section-hero {
  padding: var(--spacing-xl) 0 var(--spacing-lg);
  margin-bottom: 0;
}

/* Typography - Ultimate Perfection */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-heading);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
  max-width: 30ch;
}

h1 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-tight);
  max-width: 20ch;
  margin-bottom: var(--spacing-lg);
}

h2 {
  font-size: 1.5rem;
  letter-spacing: var(--letter-spacing-tight);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  max-width: 25ch;
}

h3 {
  font-size: 1.25rem;
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: var(--font-weight-medium);
}

h4 {
  font-size: 1rem;
  margin-bottom: var(--spacing-xs);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: var(--font-weight-medium);
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  max-width: 60ch;
  letter-spacing: var(--letter-spacing-normal);
}

p:last-child {
  margin-bottom: 0;
}

/* Accessibility-specific styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}



/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .transition-fade {
    transition: none !important;
    filter: none !important;
    transform: none !important;
  }
}

a {
  color: var(--color-text);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  border-bottom: 1px solid var(--color-border);
  font-weight: var(--font-weight-normal);
}

a:hover {
  border-bottom-color: var(--color-text);
}

/* Active navigation link */
.active,
a.active {
  font-weight: var(--font-weight-medium);
  border-bottom-color: var(--color-text);
}

/* Focus styles for keyboard navigation */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-focus-ring);
}

/* Only show focus styles when using keyboard */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
  box-shadow: none;
}

/* Header & Navigation - Absolute Minimalism */
header {
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: var(--spacing-xl);
}

.header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

.logo a {
  color: inherit;
  border-bottom: none;
  display: flex;
  align-items: center;
  transition: opacity var(--transition-fast) ease;
}

.logo a:hover {
  border-bottom: none;
  opacity: 0.8;
}

.logo img {
  display: inline-block;
  margin-right: 0.5rem;
  width: 20px;
  height: 20px;
}

nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
}

nav li {
  margin-left: var(--spacing-lg);
}

nav a {
  color: var(--color-text-light);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-small);
  letter-spacing: var(--letter-spacing-wide);
  border-bottom: none;
  transition: color var(--transition-fast) ease;
}

nav a:hover {
  color: var(--color-text);
  border-bottom: none;
}

nav a.active {
  color: var(--color-text);
  font-weight: var(--font-weight-normal);
}

/* Navigation dropdown */
.nav-more {
  position: relative;
}

.more-button {
  display: flex;
  align-items: center;
  color: var(--color-text-light);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-small);
  letter-spacing: var(--letter-spacing-wide);
  cursor: pointer;
  transition: color var(--transition-fast) ease;
  border: none;
  background: none;
  padding: 0;
  line-height: 1;
}

.more-button:hover {
  color: var(--color-text);
}

.more-button-icon {
  margin-left: 4px;
  transition: transform var(--transition-fast) ease;
}

.nav-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-sm);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity var(--transition-fast) ease,
              visibility var(--transition-fast) ease,
              transform var(--transition-fast) ease;
  z-index: 100;
  min-width: 180px;
}

.nav-more:hover .nav-dropdown,
.nav-dropdown:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-more:hover .more-button-icon {
  transform: rotate(180deg);
}

.nav-dropdown ul {
  display: block;
  padding: 0;
}

.nav-dropdown li {
  margin: 0;
  padding: var(--spacing-xs) 0;
  white-space: nowrap;
}

.nav-dropdown a {
  display: block;
  padding: 4px var(--spacing-xs);
  border-radius: 2px;
}

.nav-dropdown a:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Hero Section - Typographic Perfection */
.hero {
  text-align: center;
  max-width: 650px;
  margin: 0 auto;
}

.hero h1 {
  margin: 0 auto var(--spacing-md);
  font-size: 2rem;
  line-height: 1.2;
  max-width: 16ch;
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-tight);
}

.hero p {
  font-size: var(--font-size-base);
  max-width: 32ch;
  margin: 0 auto var(--spacing-xl);
  color: var(--color-text-light);
  line-height: var(--line-height-base);
  letter-spacing: var(--letter-spacing-normal);
}

/* Animation container for the agent demo */
.agent-animation {
  margin: var(--spacing-lg) auto;
  height: 200px;
  position: relative;
  max-width: 500px;
  overflow: hidden;
}

.agent-prompt {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-text);
  text-align: center;
  padding: var(--spacing-xs);
  line-height: 1.25;
  opacity: 0;
  transform: translateY(10px);
}

.agent-response {
  position: absolute;
  left: 0;
  top: 50px;
  width: 100%;
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-text-light);
  text-align: center;
  padding: var(--spacing-xs);
  line-height: 1.25;
  opacity: 0;
  transform: translateY(10px);
}

.agent-response-2 {
  position: absolute;
  left: 0;
  top: 100px;
  width: 100%;
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-text-light);
  text-align: center;
  padding: var(--spacing-xs);
  line-height: 1.25;
  opacity: 0;
  transform: translateY(10px);
}

.agent-response-3 {
  position: absolute;
  left: 0;
  top: 150px;
  width: 100%;
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-accent);
  text-align: center;
  padding: var(--spacing-xs);
  line-height: 1.25;
  opacity: 0;
  transform: translateY(10px);
}

/* Animation classes that will be added after country selection */
.animate-prompt {
  animation: fadeInUp var(--transition-medium) ease forwards;
}

.animate-response {
  animation: fadeInUp var(--transition-medium) ease forwards 1.2s;
}

.animate-response-2 {
  animation: fadeInUp var(--transition-medium) ease forwards 2.4s;
}

.animate-response-3 {
  animation: fadeInUp var(--transition-medium) ease forwards 3.6s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

/* Content Sections - Refined for Better Rhythm and Spacing */
.content-section {
  margin-bottom: var(--spacing-xl);
  max-width: 100%;
}

.content-section h2 {
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
}

.content-section h3 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--color-text);
}

.content-section p {
  color: var(--color-text);
}

/* Cards - Absolute Minimalism */
.card {
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.card h3, .card h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

.card p {
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  max-width: 55ch;
}

.card p:last-child {
  margin-bottom: 0;
}

.card ul, .card ol {
  padding-left: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-base);
}

.card li {
  margin-bottom: var(--spacing-xs);
}

/* Grid Layout - Refined for Better Spacing */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

/* Images - Simplified */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

.logo img {
  display: inline-block;
}

/* Footer - Pure Minimalism */
footer {
  padding: var(--spacing-lg) 0;
  border-top: 1px solid var(--color-border);
  text-align: center;
  color: var(--color-text-light);
  margin-top: var(--spacing-xxl);
}

footer p {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-small);
  letter-spacing: var(--letter-spacing-wide);
  line-height: 1.6;
}

footer p:last-child {
  margin-bottom: 0;
}

.contact-info {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-small);
  letter-spacing: var(--letter-spacing-wide);
}

.contact-info a {
  color: var(--color-text-light);
  border-bottom: none;
  transition: color var(--transition-fast) ease;
}

.contact-info a:hover {
  color: var(--color-text);
  border-bottom: none;
}

.footer-links {
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--color-border);
  font-size: var(--font-size-small);
}

.footer-links a {
  color: var(--color-text-light);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color var(--transition-fast) ease;
  padding: 0 var(--spacing-xs);
}

.footer-links a:hover,
.footer-links a:focus {
  color: var(--color-text);
  border-bottom-color: var(--color-text);
}

/* Utilities - Simplified and Refined */
.text-center {
  text-align: center;
}

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

/* Responsive Design - Ultimate Simplicity */
@media (max-width: 768px) {
  :root {
    --spacing-lg: 2.618rem;
    --spacing-xl: 4.236rem;
    --spacing-xxl: 5.5rem;
  }

  .container {
    padding: 0 var(--spacing-sm);
  }

  h1, h2, h3, h4, h5, h6, p {
    max-width: 100%;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.375rem;
    margin-top: var(--spacing-lg);
  }

  h3 {
    font-size: 1.125rem;
  }

  .hero h1 {
    font-size: 1.75rem;
  }

  .hero p {
    font-size: var(--font-size-base);
  }

  .header-inner {
    flex-direction: column;
  }

  nav {
    margin-top: var(--spacing-md);
    width: 100%;
  }

  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }

  nav li {
    margin: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  html {
    font-size: 15px;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .section {
    padding: var(--spacing-md) 0;
  }

  .section-hero {
    padding: var(--spacing-lg) 0 var(--spacing-md);
  }

  footer {
    margin-top: var(--spacing-xl);
  }

  .floating-pad {
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
  }
}

/* Floating Pad Styles */
.floating-pad {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  transition: all var(--transition-fast) ease;
}

.floating-pad:hover {
  border-color: var(--color-text-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.floating-pad a {
  display: block;
  border-bottom: none;
  color: var(--color-text);
}

.floating-pad a:hover {
  border-bottom: none;
}

.floating-pad-content {
  display: flex;
  align-items: center;
  padding: 10px 16px;
}

.floating-pad-text {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-wide);
  color: var(--color-text-light);
}

.floating-pad-arrow {
  margin-left: 8px;
  font-size: 1rem;
  transition: transform var(--transition-fast) ease;
  color: var(--color-text-light);
}

.floating-pad:hover .floating-pad-text,
.floating-pad:hover .floating-pad-arrow {
  color: var(--color-text);
}

.floating-pad:hover .floating-pad-arrow {
  transform: translateX(3px);
}

/* ========================================
   COUNTRY MODAL STYLES
   ======================================== */

/* Country Selection Modal Styles - Refined for Perfect Typography */

/* Modal Container */
.country-modal {
  display: none; /* Will be set to flex by JavaScript */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  opacity: 0;
  transition: opacity var(--transition-medium) cubic-bezier(0.16, 1, 0.3, 1);
  justify-content: center;
  align-items: center;
}

/* Modal Content */
.country-modal-content {
  background-color: var(--color-background);
  width: 90%;
  max-width: 460px;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transform: translateY(20px) scale(0.98);
  opacity: 0;
  transition: transform var(--transition-medium) cubic-bezier(0.16, 1, 0.3, 1),
              opacity var(--transition-medium) cubic-bezier(0.16, 1, 0.3, 1);
}

.country-modal.active .country-modal-content {
  transform: translateY(0) scale(1);
  opacity: 1;
}

/* Modal Header */
.country-modal-header {
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
}

.country-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-text);
}

/* Modal Body */
.country-modal-body {
  padding: 0 var(--spacing-md) var(--spacing-md);
}

.country-modal-body p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-light);
  font-size: var(--font-size-base);
  line-height: 1.6;
  max-width: 42ch;
}

/* Country Select */
.country-select-container {
  margin-bottom: var(--spacing-md);
  position: relative;
}

#country-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background-color: var(--color-background);
  color: var(--color-text);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  transition: border-color var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

#country-select:focus {
  outline: none;
  border-color: var(--color-text-light);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.03);
}

/* Cache Setting */
.cache-setting {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.cache-setting input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.cache-setting label {
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  line-height: 1.5;
  user-select: none;
}

.cache-setting label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-background);
  transition: all var(--transition-fast) ease;
}

.cache-setting label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 4px;
  width: 6px;
  height: 10px;
  border: solid var(--color-background);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg) scale(0);
  opacity: 0;
  transition: all var(--transition-fast) ease;
}

.cache-setting input[type="checkbox"]:checked + label:before {
  background-color: var(--color-text);
  border-color: var(--color-text);
}

.cache-setting input[type="checkbox"]:checked + label:after {
  opacity: 1;
  transform: rotate(45deg) scale(1);
}

.cache-setting input[type="checkbox"]:focus + label:before {
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.03);
}

/* Modal Footer */
.country-modal-footer {
  padding: var(--spacing-md);
  display: flex;
  justify-content: flex-end;
}

/* Button Styles */
.btn-primary {
  background-color: var(--color-text);
  color: var(--color-background);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-primary);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast) cubic-bezier(0.16, 1, 0.3, 1);
  letter-spacing: 0.01em;
}

.btn-primary:hover {
  background-color: #333;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(1px);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Body Blur Effect */
body.modal-open {
  overflow: hidden;
}

/* ========================================
   CHANGELOG MODAL STYLES
   ======================================== */

/* Changelog Modal Styles */
.changelog-button {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 999;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.changelog-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.changelog-button img {
  width: 24px;
  height: 24px;
}

.changelog-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.changelog-modal.show {
  opacity: 1;
  visibility: visible;
}

.changelog-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 700px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
}

.changelog-close {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.changelog-close::before,
.changelog-close::after {
  content: '';
  position: absolute;
  width: 18px;
  height: 2px;
  background-color: #333;
}

.changelog-close::before {
  transform: rotate(45deg);
}

.changelog-close::after {
  transform: rotate(-45deg);
}

.changelog-title {
  font-size: 24px;
  margin-top: 0;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.changelog-body {
  font-size: 16px;
  line-height: 1.6;
}

.changelog-body h2 {
  font-size: 20px;
  margin-top: 24px;
  margin-bottom: 8px;
}

.changelog-body h3 {
  font-size: 18px;
  margin-top: 16px;
  margin-bottom: 8px;
}

.changelog-body ul {
  margin-top: 8px;
  margin-bottom: 16px;
  padding-left: 24px;
}

.changelog-body li {
  margin-bottom: 4px;
}

/* ========================================
   SOTA PAGE STYLES
   ======================================== */

/* SOTA (State-of-the-Art) Page Styles */

/* Hero Section */
.sota-hero {
  background: linear-gradient(to bottom, var(--color-light), var(--color-background));
  padding: var(--spacing-xl) 0;
}

/* Model Stats */
.model-stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: var(--spacing-lg);
  gap: var(--spacing-md);
}

.stat {
  flex: 1;
  min-width: 120px;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  text-align: center;
  transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
  letter-spacing: var(--letter-spacing-tight);
}

.stat-label {
  display: block;
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

/* Feature List */
.feature-list {
  list-style: none;
  padding: 0;
  margin: var(--spacing-md) 0;
}

.feature-list li {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-md);
  position: relative;
}

.feature-list li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.5em;
  width: 6px;
  height: 6px;
  background-color: var(--color-text);
  border-radius: 50%;
}

/* Code Block */
.code-block {
  background-color: var(--color-light);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: monospace;
  font-size: var(--font-size-small);
  line-height: 1.5;
}

.code-block code {
  display: block;
  color: var(--color-text);
}

/* Use Cases */
.use-case-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;
}

.use-case-card {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.use-case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.use-case-card h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
}

.use-case-card p {
  margin: 0;
  color: var(--color-text-light);
}

/* Benchmarks */
.benchmark-list {
  list-style: none;
  padding: 0;
  margin: var(--spacing-md) 0;
}

.benchmark-list li {
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-md);
  position: relative;
}

.benchmark-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
}

/* CTA Section */
.cta-section {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-lg);
  background-color: var(--color-light);
  border-radius: 4px;
  text-align: center;
}

/* ========================================
   WORKSPACE STYLES
   ======================================== */

/* Workspace Styles */

/* Login Form */
.workspace-login {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-light);
}

.login-form {
  max-width: 400px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast) ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-text);
  box-shadow: 0 0 0 2px var(--color-focus-ring);
}

.form-group input[aria-invalid="true"] {
  border-color: var(--color-error);
}

.form-help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-small);
  color: var(--color-text-light);
}

.btn-secondary {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-text);
  border-radius: 4px;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast) ease, color var(--transition-fast) ease;
  text-align: center;
  text-decoration: none;
}

.btn-secondary:hover,
.btn-secondary:focus {
  background-color: var(--color-text);
  color: var(--color-background);
}

.login-divider {
  position: relative;
  text-align: center;
  margin: var(--spacing-md) 0;
}

.login-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-border);
  z-index: 1;
}

.login-divider span {
  position: relative;
  display: inline-block;
  padding: 0 var(--spacing-sm);
  background-color: var(--color-light);
  color: var(--color-text-light);
  font-size: var(--font-size-small);
  z-index: 2;
}

.create-account {
  text-align: center;
  margin-top: var(--spacing-md);
}

.create-account p {
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-light);
}

/* Workspace Features */
.workspace-features {
  margin: var(--spacing-xl) 0;
}

.workspace-features .grid {
  margin-top: var(--spacing-md);
}

.workspace-features .card {
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.workspace-features .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Workspace Security */
.workspace-security {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-lg);
  background-color: var(--color-light);
  border-radius: 4px;
}

/* Error Messages */
.error-message {
  color: var(--color-error);
  font-size: var(--font-size-small);
  margin-top: var(--spacing-xs);
}

/* Success Messages */
.success-message {
  color: #2e7d32;
  font-size: var(--font-size-small);
  margin-top: var(--spacing-xs);
}

/* Loading Indicator */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-text);
  animation: spin 1s ease-in-out infinite;
  margin-right: var(--spacing-xs);
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ========================================
   RESPONSIVE ADJUSTMENTS FOR ALL MODULES
   ======================================== */

/* Responsive Adjustments for Country Modal */
@media (max-width: 480px) {
  .country-modal-content {
    width: 92%;
    max-width: none;
  }

  .country-modal-header h2 {
    font-size: 1.25rem;
  }

  .country-modal-body p {
    font-size: 0.95rem;
  }
}

/* Responsive Adjustments for Changelog Modal */
@media (max-width: 768px) {
  .changelog-content {
    width: 95%;
    padding: 16px;
  }

  .changelog-title {
    font-size: 20px;
  }
}

/* Responsive Adjustments for SOTA Page */
@media (max-width: 768px) {
  .model-stats {
    flex-direction: column;
    align-items: center;
  }

  .stat {
    width: 100%;
    max-width: 200px;
  }

  .use-case-grid {
    grid-template-columns: 1fr;
  }
}

/* Responsive Adjustments for Workspace */
@media (max-width: 768px) {
  .workspace-login {
    padding: var(--spacing-md);
  }

  .login-form {
    max-width: 100%;
  }
}

/* Additional Focus Styles for Accessibility */
.btn-primary:focus,
.btn-secondary:focus,
input:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* Accessibility Enhancements for SOTA */
.btn-primary:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}
