<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Country Selection Test</title>
  <link rel="stylesheet" href="css/style.css">
  <!-- Favicon -->
  <link rel="icon" href="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" type="image/png">
  <link rel="stylesheet" href="css/country-modal.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
      color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      text-align: center;
    }
    
    h1 {
      margin-bottom: 20px;
    }
    
    button {
      background-color: #333;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    
    button:hover {
      background-color: #555;
    }
  </style>
</head>
<body>
  <div>
    <h1>Country Selection Modal Test</h1>
    <p>The country selection modal should appear automatically when this page loads.</p>
    <button id="show-modal-btn">Show Modal Again</button>
  </div>

  <!-- Country Selection Modal -->
  <div id="country-modal" class="country-modal">
    <div class="country-modal-content">
      <div class="country-modal-header">
        <h2>
          <img src="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" alt="Ingenuity Logo" width="24" height="24" style="vertical-align: middle; margin-right: 8px;">
          Welcome to Ingenuity
        </h2>
      </div>
      <div class="country-modal-body">
        <p>Select your location to optimize your experience and enable faster browsing with enhanced caching.</p>
        <div class="country-select-container">
          <select id="country-select" aria-label="Select your country">
            <option value="" disabled selected>Where are you browsing from?</option>
            <option value="us">United States</option>
            <option value="ca">Canada</option>
            <option value="uk">United Kingdom</option>
            <option value="au">Australia</option>
            <option value="de">Germany</option>
            <option value="fr">France</option>
            <option value="jp">Japan</option>
            <option value="in">India</option>
            <option value="br">Brazil</option>
            <option value="other">Somewhere Else Entirely</option>
          </select>
        </div>
        <div class="cache-setting">
          <input type="checkbox" id="enable-cache" checked>
          <label for="enable-cache">Enable cache for the most harmonious browsing experience possible</label>
        </div>
      </div>
      <div class="country-modal-footer">
        <button id="confirm-country" class="btn-primary">Confirm Selection</button>
      </div>
    </div>
  </div>

  <script>
    // Show modal immediately when page loads
    window.onload = function() {
      showModal();
      
      // Add event listener to button
      document.getElementById('show-modal-btn').addEventListener('click', showModal);
      
      // Add event listener to confirm button
      document.getElementById('confirm-country').addEventListener('click', function() {
        hideModal();
        alert('Country selection confirmed! Cache settings enabled.');
      });
    };
    
    function showModal() {
      var modal = document.getElementById('country-modal');
      modal.style.display = 'flex';
      
      // Use setTimeout to ensure the transition works
      setTimeout(function() {
        modal.style.opacity = '1';
        
        var modalContent = modal.querySelector('.country-modal-content');
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'translateY(0) scale(1)';
        
        document.body.style.overflow = 'hidden';
      }, 10);
    }
    
    function hideModal() {
      var modal = document.getElementById('country-modal');
      modal.style.opacity = '0';
      
      var modalContent = modal.querySelector('.country-modal-content');
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'translateY(20px) scale(0.98)';
      
      setTimeout(function() {
        modal.style.display = 'none';
        document.body.style.overflow = '';
      }, 400);
    }
  </script>
</body>
</html>
