<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Favicon -->
  <link rel="icon" href="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" type="image/png">
  <title>GPT-2 Tokenizer</title>
  <link rel="stylesheet" href="css/style.css">
  <style>
    body {
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }
    
    .tokenizer-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      margin-bottom: 2rem;
    }
    
    textarea {
      width: 100%;
      min-height: 150px;
      padding: 1rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: inherit;
      font-size: 1rem;
      margin-bottom: 1rem;
      resize: vertical;
    }
    
    button {
      background-color: #4a6cf7;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #3a5ce5;
    }
    
    button:disabled {
      background-color: #a0a0a0;
      cursor: not-allowed;
    }
    
    .results {
      display: none;
      margin-top: 2rem;
    }
    
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }
    
    .metric-card {
      background-color: #f5f7ff;
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;
    }
    
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #4a6cf7;
      margin-bottom: 0.5rem;
    }
    
    .metric-label {
      font-size: 0.9rem;
      color: #666;
    }
    
    .token-visualization {
      background-color: #f5f7ff;
      border-radius: 8px;
      padding: 1.5rem;
      margin-top: 2rem;
    }
    
    .token-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin: 1rem 0;
    }
    
    .token {
      display: inline-block;
      background-color: #e0e7ff;
      color: #4a6cf7;
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
      font-family: monospace;
    }
    
    .algorithm-metrics {
      margin-top: 2rem;
    }
    
    .algorithm-metrics ul {
      list-style-type: none;
      padding: 0;
    }
    
    .algorithm-metrics li {
      background-color: #f5f7ff;
      padding: 0.75rem;
      border-radius: 4px;
      margin-bottom: 0.5rem;
    }
    
    .loading {
      display: inline-block;
      width: 1rem;
      height: 1rem;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-left: 0.5rem;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>
        <img src="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" alt="Ingenuity Logo" width="40" height="40" style="vertical-align: middle; margin-right: 10px;">
        GPT-2 Tokenizer
      </h1>
      <p>Analyze your text using the GPT-2 tokenizer and golden ratio principles</p>
    </header>
    
    <div class="tokenizer-container">
      <h2>Enter Your Text</h2>
      <textarea id="text-input" placeholder="Enter text to analyze...">This is a simple test of the GPT-2 tokenizer.</textarea>
      <button id="analyze-button">Analyze Text</button>
    </div>
    
    <div id="results" class="results">
      <h2>Analysis Results</h2>
      
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-value" id="token-count">0</div>
          <div class="metric-label">Total Tokens</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value" id="efficiency">0%</div>
          <div class="metric-label">Efficiency Score</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value" id="potential">0%</div>
          <div class="metric-label">Optimization Potential</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-value" id="alignment">0%</div>
          <div class="metric-label">Golden Ratio Alignment</div>
        </div>
      </div>
      
      <div class="token-visualization">
        <h3>Token Visualization</h3>
        <p>Here's how GPT-2 tokenized your text:</p>
        <div class="token-list" id="token-display"></div>
        <p><em>Note: "Ġ" represents a space before the token.</em></p>
      </div>
      
      <div class="algorithm-metrics">
        <h3>Algorithm Metrics</h3>
        <ul id="metrics-list"></ul>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const analyzeButton = document.getElementById('analyze-button');
      const textInput = document.getElementById('text-input');
      const resultsDiv = document.getElementById('results');
      const tokenCountElement = document.getElementById('token-count');
      const efficiencyElement = document.getElementById('efficiency');
      const potentialElement = document.getElementById('potential');
      const alignmentElement = document.getElementById('alignment');
      const tokenDisplay = document.getElementById('token-display');
      const metricsList = document.getElementById('metrics-list');
      
      analyzeButton.addEventListener('click', async function() {
        const text = textInput.value.trim();
        // Remove previous error message if any
        let prevError = document.getElementById('analyze-error');
        if (prevError) prevError.remove();
        if (text.length === 0) {
          const errorMsg = document.createElement('div');
          errorMsg.id = 'analyze-error';
          errorMsg.style.color = 'red';
          errorMsg.style.margin = '1rem 0';
          errorMsg.textContent = 'Please enter some text to analyze.';
          textInput.parentNode.appendChild(errorMsg);
          return;
        }
        // Show loading state
        const originalButtonText = analyzeButton.textContent;
        analyzeButton.innerHTML = 'Analyzing <span class="loading"></span>';
        analyzeButton.disabled = true;
        resultsDiv.style.display = 'none';
        try {
          // Make API request
          const response = await fetch('/api/analyze', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text }),
          });
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          const data = await response.json();
          // Update metrics
          tokenCountElement.textContent = data.tokenCount ?? 0;
          efficiencyElement.textContent = (data.efficiency !== undefined ? Math.round(data.efficiency) : 0) + '%';
          potentialElement.textContent = (data.potential !== undefined ? Math.round(data.potential) : 0) + '%';
          alignmentElement.textContent = (data.alignment !== undefined ? Math.round(data.alignment) : 0) + '%';
          // Display tokens
          tokenDisplay.innerHTML = '';
          if (data.tokens && Array.isArray(data.tokens) && data.tokens.length > 0) {
            data.tokens.forEach(token => {
              const tokenElement = document.createElement('span');
              tokenElement.className = 'token';
              tokenElement.textContent = token.string || '';
              tokenElement.title = `Token ID: ${token.id}`;
              tokenDisplay.appendChild(tokenElement);
            });
            if (data.tokenCount > data.tokens.length) {
              const note = document.createElement('p');
              note.innerHTML = `<em>Showing ${data.tokens.length} of ${data.tokenCount} total tokens...</em>`;
              tokenDisplay.appendChild(note);
            }
          } else {
            tokenDisplay.innerHTML = '<p>Token visualization not available.</p>';
          }
          // Display algorithm metrics
          metricsList.innerHTML = '';
          const metrics = [
            `Sentence count: ${data.sentenceCount ?? 0}`,
            `Average sentence length: ${data.avgSentenceLength ?? 0} words`,
            `Golden ratio target: ${data.goldenRatioTarget ?? 16.18} words per sentence`,
            `Word length average: ${data.avgWordLength ?? 0} characters`,
            `Word count: ${data.wordCount ?? 0}`,
            `Character count: ${data.charCount ?? 0}`,
            `Token-to-word ratio: ${(data.tokenCount / (data.wordCount || 1)).toFixed(2)}`
          ];
          metrics.forEach(metric => {
            const li = document.createElement('li');
            li.textContent = metric;
            metricsList.appendChild(li);
          });
          // Show results
          resultsDiv.style.display = 'block';
          resultsDiv.scrollIntoView({ behavior: 'smooth' });
        } catch (error) {
          console.error('Error:', error);
          const errorMsg = document.createElement('div');
          errorMsg.id = 'analyze-error';
          errorMsg.style.color = 'red';
          errorMsg.style.margin = '1rem 0';
          errorMsg.textContent = `Error analyzing text: ${error.message}`;
          textInput.parentNode.appendChild(errorMsg);
        } finally {
          // Reset button
          analyzeButton.innerHTML = originalButtonText;
          analyzeButton.disabled = false;
        }
      });
    });
  </script>
</body>
</html>
