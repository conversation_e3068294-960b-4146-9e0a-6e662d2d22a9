<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Favicon -->
  <link rel="icon" href="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" type="image/png">
  <title>Simple Tokenizer</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    textarea {
      width: 100%;
      height: 100px;
      margin-bottom: 10px;
      padding: 10px;
    }
    .token {
      display: inline-block;
      padding: 4px 8px;
      background-color: #e0f7fa;
      color: #006064;
      border-radius: 4px;
      font-family: monospace;
      font-size: 14px;
      margin: 2px;
    }
    .token-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>
    <img src="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" alt="Ingenuity Logo" width="30" height="30" style="vertical-align: middle; margin-right: 10px;">
    Simple Tokenizer
  </h1>
  <textarea id="text-input" placeholder="Enter text to analyze...">this is simple test</textarea>
  <button id="analyze-button">Analyze</button>
  
  <h2>Results:</h2>
  <div id="results" style="display: none;">
    <h3>Metrics:</h3>
    <ul id="metrics-list"></ul>
    
    <h3>Tokens:</h3>
    <div id="token-display" class="token-list"></div>
  </div>
  
  <h2>Raw Response:</h2>
  <pre id="response"></pre>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const analyzeButton = document.getElementById('analyze-button');
      const textInput = document.getElementById('text-input');
      const responseElement = document.getElementById('response');
      const resultsElement = document.getElementById('results');
      const metricsList = document.getElementById('metrics-list');
      const tokenDisplay = document.getElementById('token-display');

      analyzeButton.addEventListener('click', async function() {
        const text = textInput.value.trim();
        
        if (text.length === 0) {
          alert('Please enter some text to analyze.');
          return;
        }
        
        // Disable button during analysis
        analyzeButton.disabled = true;
        analyzeButton.textContent = 'Analyzing...';
        
        try {
          // Make the API call
          const response = await fetch('/api/analyze', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text }),
          });
          
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          
          const data = await response.json();
          
          // Display the raw response
          responseElement.textContent = JSON.stringify(data, null, 2);
          
          // Display metrics
          metricsList.innerHTML = '';
          const metrics = [
            `Token Count: ${data.tokenCount}`,
            `Efficiency: ${Math.round(data.efficiency)}%`,
            `Optimization Potential: ${Math.round(data.potential)}%`,
            `Golden Ratio Alignment: ${Math.round(data.alignment)}%`,
            `Word Count: ${data.wordCount}`,
            `Character Count: ${data.charCount}`,
            `Sentence Count: ${data.sentenceCount}`
          ];
          
          metrics.forEach(metric => {
            const li = document.createElement('li');
            li.textContent = metric;
            metricsList.appendChild(li);
          });
          
          // Display tokens
          tokenDisplay.innerHTML = '';
          if (data.tokens && Array.isArray(data.tokens)) {
            data.tokens.forEach(token => {
              const tokenElement = document.createElement('span');
              tokenElement.className = 'token';
              tokenElement.textContent = token.string;
              tokenElement.title = `Token ID: ${token.id}`;
              tokenDisplay.appendChild(tokenElement);
            });
          } else {
            tokenDisplay.textContent = 'No token information available';
          }
          
          // Show results
          resultsElement.style.display = 'block';
        } catch (error) {
          console.error('Error:', error);
          responseElement.textContent = `Error: ${error.message}`;
        } finally {
          // Re-enable button
          analyzeButton.disabled = false;
          analyzeButton.textContent = 'Analyze';
        }
      });
    });
  </script>
</body>
</html>
