/* Country Selection Modal Styles - Refined for Perfect Typography */

/* Modal Container */
.country-modal {
  display: none; /* Will be set to flex by JavaScript */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  opacity: 0;
  transition: opacity var(--transition-medium) cubic-bezier(0.16, 1, 0.3, 1);
  justify-content: center;
  align-items: center;
}

/* Modal Content */
.country-modal-content {
  background-color: var(--color-background);
  width: 90%;
  max-width: 460px;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transform: translateY(20px) scale(0.98);
  opacity: 0;
  transition: transform var(--transition-medium) cubic-bezier(0.16, 1, 0.3, 1),
              opacity var(--transition-medium) cubic-bezier(0.16, 1, 0.3, 1);
}

.country-modal.active .country-modal-content {
  transform: translateY(0) scale(1);
  opacity: 1;
}

/* Modal Header */
.country-modal-header {
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
}

.country-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-text);
}

/* Modal Body */
.country-modal-body {
  padding: 0 var(--spacing-md) var(--spacing-md);
}

.country-modal-body p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-light);
  font-size: var(--font-size-base);
  line-height: 1.6;
  max-width: 42ch;
}

/* Country Select */
.country-select-container {
  margin-bottom: var(--spacing-md);
  position: relative;
}

#country-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background-color: var(--color-background);
  color: var(--color-text);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  transition: border-color var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

#country-select:focus {
  outline: none;
  border-color: var(--color-text-light);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.03);
}

/* Cache Setting */
.cache-setting {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.cache-setting input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.cache-setting label {
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  line-height: 1.5;
  user-select: none;
}

.cache-setting label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-background);
  transition: all var(--transition-fast) ease;
}

.cache-setting label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 4px;
  width: 6px;
  height: 10px;
  border: solid var(--color-background);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg) scale(0);
  opacity: 0;
  transition: all var(--transition-fast) ease;
}

.cache-setting input[type="checkbox"]:checked + label:before {
  background-color: var(--color-text);
  border-color: var(--color-text);
}

.cache-setting input[type="checkbox"]:checked + label:after {
  opacity: 1;
  transform: rotate(45deg) scale(1);
}

.cache-setting input[type="checkbox"]:focus + label:before {
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.03);
}

/* Modal Footer */
.country-modal-footer {
  padding: var(--spacing-md);
  display: flex;
  justify-content: flex-end;
}

/* Button Styles */
.btn-primary {
  background-color: var(--color-text);
  color: var(--color-background);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-primary);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast) cubic-bezier(0.16, 1, 0.3, 1);
  letter-spacing: 0.01em;
}

.btn-primary:hover {
  background-color: #333;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(1px);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Body Blur Effect */
body.modal-open {
  overflow: hidden;
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .country-modal-content {
    width: 92%;
    max-width: none;
  }

  .country-modal-header h2 {
    font-size: 1.25rem;
  }

  .country-modal-body p {
    font-size: 0.95rem;
  }
}
