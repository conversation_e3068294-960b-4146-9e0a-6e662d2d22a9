/* SOTA (State-of-the-Art) Page Styles */

/* Hero Section */
.sota-hero {
  background: linear-gradient(to bottom, var(--color-light), var(--color-background));
  padding: var(--spacing-xl) 0;
}



/* Model Stats */
.model-stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: var(--spacing-lg);
  gap: var(--spacing-md);
}

.stat {
  flex: 1;
  min-width: 120px;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  text-align: center;
  transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
  letter-spacing: var(--letter-spacing-tight);
}

.stat-label {
  display: block;
  font-size: var(--font-size-small);
  color: var(--color-text-light);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

/* Feature List */
.feature-list {
  list-style: none;
  padding: 0;
  margin: var(--spacing-md) 0;
}

.feature-list li {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-md);
  position: relative;
}

.feature-list li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.5em;
  width: 6px;
  height: 6px;
  background-color: var(--color-text);
  border-radius: 50%;
}

/* Code Block */
.code-block {
  background-color: var(--color-light);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: monospace;
  font-size: var(--font-size-small);
  line-height: 1.5;
}

.code-block code {
  display: block;
  color: var(--color-text);
}

/* Use Cases */
.use-case-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;
}

.use-case-card {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.use-case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.use-case-card h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
}

.use-case-card p {
  margin: 0;
  color: var(--color-text-light);
}

/* Benchmarks */
.benchmark-list {
  list-style: none;
  padding: 0;
  margin: var(--spacing-md) 0;
}

.benchmark-list li {
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-md);
  position: relative;
}

.benchmark-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--color-text);
  font-weight: var(--font-weight-bold);
}

/* CTA Section */
.cta-section {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-lg);
  background-color: var(--color-light);
  border-radius: 4px;
  text-align: center;
}

.btn-primary {
  display: inline-block;
  padding: 12px 24px;
  background-color: var(--color-text);
  color: var(--color-background);
  border: none;
  border-radius: 4px;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: background-color var(--transition-fast) ease;
  margin-top: var(--spacing-md);
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: #000;
  color: var(--color-background);
  text-decoration: none;
  border-bottom: none;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .model-stats {
    flex-direction: column;
    align-items: center;
  }

  .stat {
    width: 100%;
    max-width: 200px;
  }

  .use-case-grid {
    grid-template-columns: 1fr;
  }
}

/* Accessibility Enhancements */
.btn-primary:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}
