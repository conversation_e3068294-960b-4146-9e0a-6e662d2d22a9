/* Workspace Styles */

/* Login Form */
.workspace-login {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-light);
}

.login-form {
  max-width: 400px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast) ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-text);
  box-shadow: 0 0 0 2px var(--color-focus-ring);
}

.form-group input[aria-invalid="true"] {
  border-color: var(--color-error);
}

.form-help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-small);
  color: var(--color-text-light);
}

.btn-primary {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--color-text);
  color: var(--color-background);
  border: none;
  border-radius: 4px;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast) ease;
  width: 100%;
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: #000;
}

.btn-secondary {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-text);
  border-radius: 4px;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast) ease, color var(--transition-fast) ease;
  text-align: center;
  text-decoration: none;
}

.btn-secondary:hover,
.btn-secondary:focus {
  background-color: var(--color-text);
  color: var(--color-background);
}

.login-divider {
  position: relative;
  text-align: center;
  margin: var(--spacing-md) 0;
}

.login-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-border);
  z-index: 1;
}

.login-divider span {
  position: relative;
  display: inline-block;
  padding: 0 var(--spacing-sm);
  background-color: var(--color-light);
  color: var(--color-text-light);
  font-size: var(--font-size-small);
  z-index: 2;
}

.create-account {
  text-align: center;
  margin-top: var(--spacing-md);
}

.create-account p {
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-light);
}

/* Workspace Features */
.workspace-features {
  margin: var(--spacing-xl) 0;
}

.workspace-features .grid {
  margin-top: var(--spacing-md);
}

.workspace-features .card {
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
}

.workspace-features .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Workspace Security */
.workspace-security {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-lg);
  background-color: var(--color-light);
  border-radius: 4px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .workspace-login {
    padding: var(--spacing-md);
  }
  
  .login-form {
    max-width: 100%;
  }
}

/* Focus Styles for Accessibility */
.btn-primary:focus,
.btn-secondary:focus,
input:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* Error Messages */
.error-message {
  color: var(--color-error);
  font-size: var(--font-size-small);
  margin-top: var(--spacing-xs);
}

/* Success Messages */
.success-message {
  color: #2e7d32;
  font-size: var(--font-size-small);
  margin-top: var(--spacing-xs);
}

/* Loading Indicator */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-text);
  animation: spin 1s ease-in-out infinite;
  margin-right: var(--spacing-xs);
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
