<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="#ffffff"/>

  <!-- Golden ratio grid lines -->
  <g stroke="#f5f5f5" stroke-width="1">
    <!-- Vertical golden ratio lines -->
    <line x1="458.8" y1="0" x2="458.8" y2="630"/>
    <line x1="741.2" y1="0" x2="741.2" y2="630"/>

    <!-- Horizontal golden ratio lines -->
    <line x1="0" y1="240.6" x2="1200" y2="240.6"/>
    <line x1="0" y1="389.4" x2="1200" y2="389.4"/>
  </g>

  <!-- Title -->
  <text x="120" y="180" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="64" font-weight="600" fill="#111111">
    Our Infrastructure
  </text>

  <!-- Subtitle -->
  <text x="120" y="250" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="32" font-weight="400" fill="#666666">
    Powering LLMs with Vercel &amp; GPU Compute
  </text>

  <!-- Infrastructure elements -->
  <!-- Server icon -->
  <g transform="translate(800, 320)">
    <rect x="-60" y="-60" width="120" height="160" rx="8" fill="#f9f9f9" stroke="#e0e0e0" stroke-width="2"/>
    <rect x="-40" y="-40" width="80" height="10" rx="2" fill="#e0e0e0"/>
    <rect x="-40" y="-20" width="80" height="10" rx="2" fill="#e0e0e0"/>
    <rect x="-40" y="0" width="80" height="10" rx="2" fill="#e0e0e0"/>
    <rect x="-40" y="20" width="80" height="10" rx="2" fill="#e0e0e0"/>
    <rect x="-40" y="40" width="40" height="10" rx="2" fill="#e0e0e0"/>
    <circle cx="0" cy="70" r="10" fill="#e0e0e0"/>
  </g>

  <!-- Cloud icon -->
  <g transform="translate(950, 250)">
    <path d="M-50,20 C-50,-10 -20,-30 10,-20 C20,-50 60,-50 80,-20 C110,-30 130,0 110,30 C120,50 100,70 80,60 C70,80 30,80 20,60 C0,70 -30,60 -40,40 C-60,40 -70,10 -50,20 Z" fill="#f9f9f9" stroke="#e0e0e0" stroke-width="2"/>
    <circle cx="0" cy="10" r="8" fill="#e0e0e0"/>
    <circle cx="30" cy="0" r="8" fill="#e0e0e0"/>
    <circle cx="60" cy="10" r="8" fill="#e0e0e0"/>
  </g>

  <!-- GPU icon -->
  <g transform="translate(850, 450)">
    <rect x="-60" y="-40" width="120" height="80" rx="4" fill="#f9f9f9" stroke="#e0e0e0" stroke-width="2"/>
    <rect x="-50" y="-30" width="100" height="60" rx="2" fill="#f5f5f5" stroke="#e0e0e0" stroke-width="1"/>
    <rect x="-40" y="-20" width="20" height="40" fill="#e0e0e0"/>
    <rect x="-10" y="-20" width="20" height="40" fill="#e0e0e0"/>
    <rect x="20" y="-20" width="20" height="40" fill="#e0e0e0"/>
    <rect x="50" y="-20" width="20" height="40" fill="#e0e0e0"/>
  </g>

  <!-- Vercel logo (simplified) -->
  <g transform="translate(650, 350)">
    <path d="M0,-40 L40,40 L-40,40 Z" fill="#111111"/>
  </g>

  <!-- Connection lines -->
  <g stroke="#e0e0e0" stroke-width="2" stroke-dasharray="5,5">
    <path d="M650,350 L800,320" fill="none"/>
    <path d="M650,350 L850,450" fill="none"/>
    <path d="M650,350 L950,250" fill="none"/>
  </g>

  <!-- Golden ratio spiral (simplified) -->
  <path d="M120,450 Q120,390 180,390 Q180,330 240,330 Q240,270 300,270 Q300,210 360,210"
        fill="none" stroke="#e0e0e0" stroke-width="2"/>

  <!-- Company name -->
  <text x="120" y="520" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="24" font-weight="500" fill="#111111">
    Synthara AI
  </text>

  <!-- URL -->
  <text x="120" y="550" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="18" font-weight="400" fill="#666666">
    inferencesyntharaai.vercel.app
  </text>
</svg>
