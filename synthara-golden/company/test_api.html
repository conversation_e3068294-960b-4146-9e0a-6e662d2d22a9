<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Favicon -->
  <link rel="icon" href="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" type="image/png">
  <title>API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    textarea {
      width: 100%;
      height: 100px;
      margin-bottom: 10px;
      padding: 10px;
    }
  </style>
</head>
<body>
  <h1>
    <img src="https://media.licdn.com/dms/image/v2/D4D0BAQGRXrchbVLnmQ/company-logo_200_200/B4DZcfVDG5GkAI-/0/1748577301437/ingenuity_amen_logo?e=1753920000&v=beta&t=t6AbqrmNNxzIwNRxMNfZiqUFGvyU6oMxHtAwHTH-WPM" alt="Ingenuity Logo" width="30" height="30" style="vertical-align: middle; margin-right: 10px;">
    API Test
  </h1>
  <textarea id="text-input" placeholder="Enter text to analyze...">this is simple test</textarea>
  <button id="analyze-button">Analyze</button>
  <h2>Response:</h2>
  <pre id="response"></pre>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const analyzeButton = document.getElementById('analyze-button');
      const textInput = document.getElementById('text-input');
      const responseElement = document.getElementById('response');

      analyzeButton.addEventListener('click', async function() {
        const text = textInput.value.trim();
        
        if (text.length === 0) {
          alert('Please enter some text to analyze.');
          return;
        }
        
        try {
          // Make the API call
          const response = await fetch('/api/analyze', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text }),
          });
          
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          
          const data = await response.json();
          
          // Display the response
          responseElement.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          console.error('Error:', error);
          responseElement.textContent = `Error: ${error.message}`;
        }
      });
    });
  </script>
</body>
</html>
