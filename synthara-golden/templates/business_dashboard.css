/* Business Dashboard Styles */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4895ef;
    --text-color: #333;
    --text-light: #666;
    --bg-color: #f9f9f9;
    --card-bg: #fff;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

/* Header */
header {
    background-color: var(--card-bg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 1.5rem;
}

nav ul li a {
    color: var(--text-light);
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
}

nav ul li a.active,
nav ul li a:hover {
    color: var(--primary-color);
}

nav ul li a.active::after,
nav ul li a:hover::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

/* Main Content */
main {
    padding: 2rem;
}

.dashboard {
    display: flex;
    gap: 2rem;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.avatar {
    margin-right: 1rem;
}

.avatar img {
    border-radius: 50%;
}

.user-details h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.user-details p {
    font-size: 0.85rem;
    color: var(--text-light);
}

.menu li {
    margin-bottom: 0.5rem;
}

.menu li a {
    display: block;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    color: var(--text-color);
    font-weight: 500;
}

.menu li a.active,
.menu li a:hover {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

/* Content */
.content {
    flex: 1;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.actions {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: background-color 0.3s;
}

.btn.primary {
    background-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background-color: var(--secondary-color);
}

.btn.secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn.secondary:hover {
    background-color: rgba(67, 97, 238, 0.1);
}

/* Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.card h3 {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.card .number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.card .trend {
    font-size: 0.85rem;
}

.trend.positive {
    color: var(--success-color);
}

.trend.negative {
    color: var(--danger-color);
}

/* Reports Section */
.reports-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.view-all {
    font-size: 0.9rem;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 600;
    color: var(--text-light);
}

tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.status {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status.open {
    background-color: rgba(72, 149, 239, 0.1);
    color: var(--accent-color);
}

.status.resolved {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.status.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    background-color: var(--card-bg);
    margin: 10% auto;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
}

.modal-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Footer */
footer {
    text-align: center;
    padding: 2rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    nav {
        margin-top: 1rem;
        width: 100%;
        overflow-x: auto;
    }
    
    nav ul {
        width: max-content;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .actions {
        margin-top: 1rem;
    }
}
