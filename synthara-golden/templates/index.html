<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web App Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        form {
            margin-bottom: 20px;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .app-list {
            margin-top: 20px;
        }
        .app-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .app-actions {
            display: flex;
            gap: 10px;
        }
        .app-actions a, .app-actions form button {
            padding: 5px 10px;
            text-decoration: none;
            color: white;
            border-radius: 3px;
        }
        .view-btn {
            background-color: #2196F3;
        }
        .preview-btn {
            background-color: #FF9800;
        }
        .delete-btn {
            background-color: #f44336;
        }
    </style>
</head>
<body>
    <h1>Web Application Generator</h1>

    <div class="container">
        <h2>Generate a New Web Application</h2>
        <form action="/generate" method="post">
            <div>
                <label for="project_name">Project Name:</label>
                <input type="text" id="project_name" name="project_name" placeholder="my_webapp" required>
            </div>
            <div>
                <label for="prompt">Describe your web application:</label>
                <textarea id="prompt" name="prompt" rows="5" placeholder="Describe the web application you want to create..." required></textarea>
            </div>
            <button type="submit">Generate Web App</button>
        </form>
    </div>

    <div class="container">
        <h2>Your Generated Web Applications</h2>
        <div class="app-list">
            {% if generated_apps %}
                {% for app in generated_apps %}
                    <div class="app-item">
                        <div>{{ app }}</div>
                        <div class="app-actions">
                            <a href="{{ url_for('view_app', app_name=app) }}" class="view-btn">View Code</a>
                            <a href="{{ url_for('preview_app', app_name=app) }}" class="preview-btn">Preview</a>
                            <form action="{{ url_for('delete_app', app_name=app) }}" method="post" onsubmit="return confirm('Are you sure you want to delete this app?');">
                                <button type="submit" class="delete-btn">Delete</button>
                            </form>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <p>No web applications generated yet.</p>
            {% endif %}
        </div>
    </div>
</body>
</html>