/* To-Do List App Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
}

header {
    margin-bottom: 30px;
    text-align: center;
    padding: 20px;
    background-color: #4a6fa5;
    color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

header p {
    color: #e0e0e0;
    margin-top: 5px;
}

.container {
    padding: 0;
}

.todo-app {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.input-section {
    display: flex;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

#task-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

#task-input:focus {
    outline: none;
    border-color: #4a6fa5;
}

#add-task-btn {
    margin-left: 10px;
    padding: 10px 20px;
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

#add-task-btn:hover {
    background-color: #3a5a8c;
}

.filter-section {
    display: flex;
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #eee;
}

.filter-btn {
    margin-right: 10px;
    padding: 5px 15px;
    background-color: transparent;
    border: 1px solid #ddd;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
}

.filter-btn.active {
    background-color: #4a6fa5;
    color: white;
    border-color: #4a6fa5;
}

#task-list {
    list-style: none;
    max-height: 400px;
    overflow-y: auto;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.task-item:last-child {
    border-bottom: none;
}

.task-checkbox {
    margin-right: 15px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-text {
    flex: 1;
    font-size: 16px;
}

.completed .task-text {
    text-decoration: line-through;
    color: #888;
}

.delete-btn {
    background-color: transparent;
    color: #ff6b6b;
    border: none;
    cursor: pointer;
    font-size: 18px;
    opacity: 0.7;
}

.delete-btn:hover {
    opacity: 1;
}

.info-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-top: 1px solid #eee;
    font-size: 14px;
    color: #666;
}

#clear-completed-btn {
    background-color: transparent;
    color: #666;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

#clear-completed-btn:hover {
    text-decoration: underline;
    color: #ff6b6b;
}

footer {
    margin-top: 30px;
    text-align: center;
    padding: 20px;
    color: #888;
    font-size: 14px;
}