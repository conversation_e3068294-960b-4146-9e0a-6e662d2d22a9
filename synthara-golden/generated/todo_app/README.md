# To-Do List Application

A simple to-do list web application built with HTML, CSS, and JavaScript.

## Features

- Add new tasks
- Mark tasks as completed
- Delete tasks
- Filter tasks (All, Active, Completed)
- Clear all completed tasks
- Tasks are saved in local storage
- Responsive design

## How to Use

1. Enter a task in the input field and press Enter or click "Add Task"
2. Click the checkbox to mark a task as completed
3. Click the "×" button to delete a task
4. Use the filter buttons to view different task categories
5. Click "Clear Completed" to remove all completed tasks