<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>To-Do List App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>To-Do List Application</h1>
        <p>Keep track of your tasks</p>
    </header>
    <main>
        <div class="container">
            <div class="todo-app">
                <div class="input-section">
                    <input type="text" id="task-input" placeholder="Add a new task...">
                    <button id="add-task-btn">Add Task</button>
                </div>
                <div class="filter-section">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="active">Active</button>
                    <button class="filter-btn" data-filter="completed">Completed</button>
                </div>
                <ul id="task-list">
                    <!-- Tasks will be added here by JavaScript -->
                </ul>
                <div class="info-section">
                    <span id="tasks-counter">0 tasks left</span>
                    <button id="clear-completed-btn">Clear Completed</button>
                </div>
            </div>
        </div>
    </main>
    <footer>
        <p>&copy; 2023 To-Do List App</p>
    </footer>
    <script src="script.js"></script>
</body>
</html>