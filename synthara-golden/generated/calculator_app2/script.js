let display = document.getElementById('display'); let clearButton = document.getElementById('clear'); let backspaceButton = document.getElementById('backspace'); let equalsButton = document.getElementById('equals'); let addButton = document.getElementById('add'); let subtractButton = document.getElementById('subtract'); let multiplyButton = document.getElementById('multiply'); let divideButton = document.getElementById('divide'); let numberButtons = document.querySelectorAll('#zero, #one, #two, #three, #four, #five, #six, #seven, #eight, #nine'); let currentNumber = ''; let previousNumber = ''; let operation = ''; // Add event listeners to number buttons numberButtons.forEach(button => { button.addEventListener('click', () => { currentNumber += button.textContent; display.value = currentNumber; }); }); // Add event listeners to operation buttons addButton.addEventListener('click', () => { previousNumber = currentNumber; currentNumber = ''; operation = 'add'; }); subtractButton.addEventListener('click', () => { previousNumber = currentNumber; currentNumber = ''; operation = 'subtract'; }); multiplyButton.addEventListener('click', () => { previousNumber = currentNumber; currentNumber = ''; operation = 'multiply'; }); divideButton.addEventListener('click', () => { previousNumber = currentNumber; currentNumber = ''; operation = 'divide'; }); // Add event listener to equals button equalsButton.addEventListener('click', () => { let result; switch (operation) { case 'add': result = parseFloat(previousNumber) + parseFloat(currentNumber); break; case 'subtract': result = parseFloat(previousNumber) - parseFloat(currentNumber); break; case 'multiply': result = parseFloat(previousNumber) * parseFloat(currentNumber); break; case 'divide': result = parseFloat(previousNumber) / parseFloat(currentNumber); break; default: result = 0; } display.value = result; currentNumber = result.toString(); previousNumber = ''; operation = ''; }); // Add event listener to clear button clearButton.addEventListener('click', () => { display.value = ''; currentNumber = ''; previousNumber = ''; operation = ''; }); // Add event listener to backspace button backspaceButton.addEventListener('click', () => { currentNumber = currentNumber.slice(0, -1); display.value = currentNumber; });